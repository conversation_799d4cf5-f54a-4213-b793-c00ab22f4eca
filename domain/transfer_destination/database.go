package transferdestination

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
	"github.com/Sera-Global/be-nbs-accounting-system/pkg/lib/dbmanager"
	"gorm.io/gorm"
)

// getByID fetches transfer destination by ID.
func (rsc TransferDestinationResource) getByID(ctx context.Context, param GetByIDParam) (TransferDestination, error) {
	var transferDestination TransferDestination

	db := dbmanager.Manager().WithContext(ctx)

	err := db.Where("id = ?", param.ID).
		Where("deleted_at IS NULL").
		First(&transferDestination).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return TransferDestination{}, nil
		}
		return TransferDestination{}, log.LogError(err, nil)
	}

	return transferDestination, nil
}
