package transferdestination

import (
	"context"

	"github.com/Sera-Global/be-nbs-accounting-system/common/log"
)

type (
	TransferDestinationDomainItf interface {
		GetByID(ctx context.Context, param GetByIDParam) (TransferDestination, error)
	}

	TransferDestinationResourceItf interface {
		getByID(ctx context.Context, param GetByIDParam) (TransferDestination, error)
	}
)

// GetByID retrieves transfer destination by ID.
func (d *TransferDestinationDomain) GetByID(ctx context.Context, param GetByIDParam) (TransferDestination, error) {
	transferDestination, err := d.resource.getByID(ctx, param)
	if err != nil {
		return TransferDestination{}, log.LogError(err, nil)
	}
	return transferDestination, nil
}
