package transferdestination

import (
	"time"
)

type TransferDestination struct {
	ID            int64      `gorm:"column:id;primary_key"`
	Code          string     `gorm:"column:code"`
	BankName      string     `gorm:"column:bank_name"`
	Branch        string     `gorm:"column:branch"`
	AccountNumber string     `gorm:"column:account_number"`
	CreatedAt     time.Time  `gorm:"column:created_at"`
	UpdatedAt     time.Time  `gorm:"column:updated_at"`
	DeletedAt     *time.Time `gorm:"column:deleted_at"`
}

// GetByIDParam represents the parameters for getting transfer destination by ID
type GetByIDParam struct {
	ID int64
}
