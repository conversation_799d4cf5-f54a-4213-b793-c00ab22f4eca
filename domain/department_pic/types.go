package departmentpic

import (
	"time"

	"github.com/Sera-Global/be-nbs-accounting-system/common/types"
	"github.com/Sera-Global/be-nbs-accounting-system/domain/department"
)

type DepartmentPic struct {
	ID           int64      `gorm:"column:id;primary_key"`
	DepartmentID int64      `gorm:"column:department_id"`
	PicBelong    string     `gorm:"column:pic_belong"`
	PicName      string     `gorm:"column:pic_name"`
	CreatedAt    time.Time  `gorm:"column:created_at"`
	UpdatedAt    time.Time  `gorm:"column:updated_at"`
	DeletedAt    *time.Time `gorm:"column:deleted_at"`

	Department department.Department `gorm:"foreignkey:DepartmentID"`
}

// GetListByDepartmentIDParam represents the parameters for getting department PICs by department ID
type GetListByDepartmentIDParam struct {
	DepartmentID int64 `json:"department_id"`
	types.BasicGetParam
}

// GetByIDWithCustomerParam represents the parameters for getting department PIC by ID with customer info
type GetByIDWithCustomerParam struct {
	ID int64
}

// DepartmentPicWithCustomer represents department PIC with customer information
type DepartmentPicWithCustomer struct {
	ID                 int64  `json:"id"`
	DepartmentID       int64  `json:"department_id"`
	DepartmentName     string `json:"department_name"`
	PicName            string `json:"pic_name"`
	CustomerID         int64  `json:"customer_id"`
	CustomerName       string `json:"customer_name"`
	CustomerPostCode   string `json:"customer_post_code"`
	CustomerPrefecture string `json:"customer_prefecture"`
	CustomerAddress    string `json:"customer_address"`
}
